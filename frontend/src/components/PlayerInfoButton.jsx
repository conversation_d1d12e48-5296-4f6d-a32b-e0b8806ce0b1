import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import PlayerInfoModal from './PlayerInfoModal';
import { API_CONFIG, getApiUrl } from '../config';
import { handleApiResponse } from '../utils/apiUtils';
import './PlayerInfoButton.css';

const PlayerInfoButton = ({ playerId, nickname, children, className = '' }) => {
  const { t } = useTranslation();
  const [isPlayerInfoModalOpen, setIsPlayerInfoModalOpen] = useState(false);
  const [playerData, setPlayerData] = useState(null);
  const [isPlayerInfoLoading, setIsPlayerInfoLoading] = useState(false);
  const [playerInfoError, setPlayerInfoError] = useState(null);

  const handlePlayerInfoClick = async () => {
    // Check if player ID is -1 (new user)
    if (playerId === -1) {
      alert(t('playerInfo.noInfo'));
      return;
    }

    setIsPlayerInfoModalOpen(true);
    setIsPlayerInfoLoading(true);
    setPlayerInfoError(null);

    try {
      const response = await fetch(getApiUrl(`${API_CONFIG.ENDPOINTS.USER_INFO}/${playerId}`));
      const data = await handleApiResponse(response);
      setPlayerData(data);
    } catch (error) {
      console.error('Error fetching player info:', error);
      setPlayerInfoError(error.message);
    } finally {
      setIsPlayerInfoLoading(false);
    }
  };

  const closePlayerInfoModal = () => {
    setIsPlayerInfoModalOpen(false);
    setPlayerData(null);
    setPlayerInfoError(null);
  };

  return (
    <>
      <button
        className={`player-info-button ${className}`}
        onClick={handlePlayerInfoClick}
        title={t('playerInfo.title')}
      >
        {children}
      </button>
      
      <PlayerInfoModal
        isOpen={isPlayerInfoModalOpen}
        onClose={closePlayerInfoModal}
        playerData={playerData}
        isLoading={isPlayerInfoLoading}
        error={playerInfoError}
        nickname={nickname}
      />
    </>
  );
};

export default PlayerInfoButton;
